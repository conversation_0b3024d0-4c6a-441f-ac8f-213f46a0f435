'use client';

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Link from 'next/link'
interface FlowRun {
  id: string;
  name: string;
  state: string;
  start_time: string | null;
  expected_start_time: string | null;
  updated: string;
  parameters: Record<string, any>;
  logs: any[];
  tasks: any[];
}

export default function FlowDetails() {
  const params = useParams();
  const flowId = params.id as string;
  
  const [flowData, setFlowData] = useState<FlowRun | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastChecked, setLastChecked] = useState<string>("");

  const fetchFlowData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/flows/${flowId}`);
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      const data = await response.json();
      setFlowData(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch flow data');
      console.error(err);
    } finally {
      setLoading(false);
      setLastChecked(new Date().toLocaleTimeString());
    }
  };

  useEffect(() => {
    fetchFlowData();
    
    // Set up polling every 5 seconds
    const intervalId = setInterval(fetchFlowData, 5000);
    
    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, [flowId]);

  // Format date to be more readable
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  // Get status color based on state
  const getStateColor = (state: string) => {
    switch (state) {
      case 'RUNNING': return 'bg-green-500 text-white';
      case 'SCHEDULED': return 'bg-blue-500 text-white';
      case 'PENDING': return 'bg-yellow-500 text-white';
      case 'PAUSED': return 'bg-gray-500 text-white';
      case 'COMPLETED': return 'bg-green-700 text-white';
      case 'FAILED': return 'bg-red-500 text-white';
      default: return 'bg-gray-300 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen p-8">
      <header className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Flow Details</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Viewing details for flow ID: {flowId}
        </p>
      </header>

      <main>
        {loading && !flowData ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-foreground"></div>
          </div>
        ) : error ? (
          <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
            <p className="text-red-700 dark:text-red-400">{error}</p>
          </div>
        ) : flowData ? (
          <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
            {/* Header with name and status */}
            <div className="p-6 border-b dark:border-gray-700">
              <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
                <div>
                  <h2 className="text-2xl font-bold">{flowData.name}</h2>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">ID: {flowData.id}</p>
                </div>
                <div className={`px-4 py-2 rounded-lg text-sm font-medium ${getStateColor(flowData.state)}`}>
                  {flowData.state}
                </div>
              </div>
            </div>
            
            {/* Timing information */}
            <div className="p-6 border-b dark:border-gray-700 bg-gray-50 dark:bg-gray-900/30">
              <h3 className="text-lg font-semibold mb-3">Timing</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Expected Start Time</p>
                  <p className="font-medium">{formatDate(flowData.expected_start_time)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Actual Start Time</p>
                  <p className="font-medium">{formatDate(flowData.start_time)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Last Updated</p>
                  <p className="font-medium">{formatDate(flowData.updated)}</p>
                </div>
              </div>
            </div>
            
            {/* Parameters */}
            {flowData.parameters && Object.keys(flowData.parameters).length > 0 && (
              <div className="p-6 border-b dark:border-gray-700">
                <h3 className="text-lg font-semibold mb-3">Parameters</h3>
                <div className="bg-gray-100 dark:bg-gray-900 rounded-lg p-4 overflow-auto">
                  <pre className="text-sm font-mono">
                    {JSON.stringify(flowData.parameters, null, 2)}
                  </pre>
                </div>
              </div>
            )}
            
            {/* Tasks */}
            {flowData.tasks && flowData.tasks.length > 0 && (
              <div className="p-6 border-b dark:border-gray-700">
                <h3 className="text-lg font-semibold mb-3">Tasks ({flowData.tasks.length})</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-900/50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Name</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">State</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Start Time</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {flowData.tasks.map((task, index) => (
                        <tr key={task.id || index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">{task.name || 'Unnamed Task'}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            <span className={`px-2 py-1 rounded-full text-xs ${getStateColor(task.state?.type || 'UNKNOWN')}`}>
                              {task.state?.type || 'UNKNOWN'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {formatDate(task.start_time)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="bg-gray-50 dark:bg-gray-900/30 rounded-lg p-8 text-center">
            <p className="text-gray-500 dark:text-gray-400">No flow data found</p>
          </div>
        )}
        
        <div className="mt-6 flex justify-between items-center">
          <Link 
            href="/"
            className="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-lg transition-colors"
          >
            Back to Dashboard
          </Link>
          
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              Last checked: {lastChecked || "Never"}
            </span>
            <button 
              onClick={fetchFlowData}
              className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21 2v6h-6M3 12a9 9 0 0 1 15-6.7L21 8M3 22v-6h6M21 12a9 9 0 0 1-15 6.7L3 16" />
              </svg>
            </button>
          </div>
        </div>
      </main>
    </div>
  );
}
