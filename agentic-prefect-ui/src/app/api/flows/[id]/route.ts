// app/api/prefect-running/route.ts
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const prefectApiKey = process.env.PREFECT_API_KEY
  const accountId = process.env.PREFECT_ACCOUNT_ID
  const workspaceId = process.env.PREFECT_WORKSPACE_ID

  if (!prefectApiKey || !accountId || !workspaceId) {
    return NextResponse.json({ error: 'Missing environment variables' }, { status: 500 })
  }

  const url = `https://api.prefect.cloud/api/accounts/${accountId}/workspaces/${workspaceId}/flow_runs/filter`

  const headers = {
    Authorization: `Bearer ${prefectApiKey}`,
    'Content-Type': 'application/json',
    'X-PREFECT-API-VERSION': '0.8.4',
  }

  const body = {
    flow_runs: {
      state: {
        type: {
          any_: ['RUNNING', 'PENDING', 'SCHEDULED', 'PAUSED'],
        },
      },
    },
    sort: "EXPECTED_START_TIME_ASC", // Optional: sort by when flows are expected to start
    limit: 50, // Optional: adjust based on how many you expect
  }

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
    })

    if (!response.ok) {
      return NextResponse.json({ error: `Prefect API error ${response.status}` }, { status: response.status })
    }

    const data = await response.json()

    const runningFlows = data.map((flow: any) => ({
      id: flow.id,
      name: flow.name,
      state: flow.state?.type,
      start_time: flow.start_time,
      expected_start_time: flow.expected_start_time,
      updated: flow.updated,
    }))

    return NextResponse.json({ running: runningFlows })
  } catch (err) {
    console.error('Prefect fetch error:', err)
    return NextResponse.json({ error: 'Failed to fetch running flows' }, { status: 500 })
  }
}
