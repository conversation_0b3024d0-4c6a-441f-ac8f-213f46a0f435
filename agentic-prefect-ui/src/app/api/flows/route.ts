// app/api/prefect-running/route.ts
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const prefectApiKey = process.env.PREFECT_API_KEY
  const accountId = process.env.PREFECT_ACCOUNT_ID
  const workspaceId = process.env.PREFECT_WORKSPACE_ID

  if (!prefectApiKey || !accountId || !workspaceId) {
    console.error('Missing environment variables');
    return NextResponse.json({ error: 'Missing environment variables' }, { status: 500 })
  }

  // Get the flow type from the query parameters, default to "lead-flow"
  const searchParams = request.nextUrl.searchParams;
  const flowType = searchParams.get('flowType') || 'lead-flow';

  const url = `https://api.prefect.cloud/api/accounts/${accountId}/workspaces/${workspaceId}/flow_runs/filter`

  const headers = {
    Authorization: `Bearer ${prefectApiKey}`,
    'Content-Type': 'application/json',
    'X-PREFECT-API-VERSION': '0.8.4',
  }

  const body = {
    "flows": {
      name: {
        any_: [flowType]
      },
    },
    sort: "START_TIME_DESC",
    limit: 50,
  }

  try {
    console.log(`Fetching flow runs for type: ${flowType}`);
    
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
      cache: 'no-store',
    })

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Prefect API error:', response.status, errorText);
      return NextResponse.json({ 
        error: `Prefect API error ${response.status}`, 
        details: errorText 
      }, { status: response.status })
    }

    const data = await response.json();

    const flowRuns = data.map((flow: any) => ({
      id: flow.id,
      name: flow.name,
      state: flow.state?.type,
      start_time: flow.start_time,
      expected_start_time: flow.expected_start_time,
      updated: flow.updated,
    }))

    return NextResponse.json({ 
      running: flowRuns,
      flowType: flowType
    })
  } catch (err) {
    console.error('Prefect fetch error:', err);
    return NextResponse.json({ 
      error: 'Failed to fetch flow runs',
      details: err instanceof Error ? err.message : String(err)
    }, { status: 500 })
  }
}
