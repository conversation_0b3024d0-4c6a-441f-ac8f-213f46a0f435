import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const prefectApiKey = process.env.PREFECT_API_KEY
  const accountId = process.env.PREFECT_ACCOUNT_ID
  const workspaceId = process.env.PREFECT_WORKSPACE_ID

  if (!prefectApiKey || !accountId || !workspaceId) {
    return NextResponse.json({ error: 'Missing environment variables' }, { status: 500 })
  }

  const url = `https://api.prefect.cloud/api/accounts/${accountId}/workspaces/${workspaceId}/deployments/filter`

  const headers = {
    Authorization: `Bearer ${prefectApiKey}`,
    'Content-Type': 'application/json',
    'X-PREFECT-API-VERSION': '0.8.4',
  }

  const body = {
    deployments: {
      name: {
        contains_: "lead-flow"
      },
      is_schedule_active: true
    },
    sort: "UPDATED_DESC",
    limit: 50
  }

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
    })

    if (!response.ok) {
      return NextResponse.json({ error: `Prefect API error ${response.status}` }, { status: response.status })
    }

    const data = await response.json()

    const leadFlowDeployments = data.map((deployment: any) => ({
      id: deployment.id,
      name: deployment.name,
      version: deployment.version,
      is_schedule_active: deployment.is_schedule_active,
      updated: deployment.updated,
      created: deployment.created,
      parameters: deployment.parameters
    }))

    return NextResponse.json({ deployments: leadFlowDeployments })
  } catch (err) {
    console.error('Prefect fetch error:', err)
    return NextResponse.json({ error: 'Failed to fetch lead-flow deployments' }, { status: 500 })
  }
}