import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const prefectApiKey = process.env.PREFECT_API_KEY
  const accountId = process.env.PREFECT_ACCOUNT_ID
  const workspaceId = process.env.PREFECT_WORKSPACE_ID

  if (!prefectApiKey || !accountId || !workspaceId) {
    console.error('Missing environment variables');
    return NextResponse.json({ error: 'Missing environment variables' }, { status: 500 })
  }

  const url = `https://api.prefect.cloud/api/accounts/${accountId}/workspaces/${workspaceId}/flows/filter`

  const headers = {
    Authorization: `Bearer ${prefectApiKey}`,
    'Content-Type': 'application/json',
    'X-PREFECT-API-VERSION': '0.8.4',
  }

  const body = {
    sort: "NAME_ASC",
    limit: 100,
  }

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
      cache: 'no-store',
    })

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Prefect API error:', response.status, errorText);
      return NextResponse.json({ 
        error: `Prefect API error ${response.status}`, 
        details: errorText 
      }, { status: response.status })
    }

    const data = await response.json();
    
    // Extract unique flow names
    const flowNames = Array.from(new Set(
      data.map((flow: any) => flow.name)
    )).sort();

    return NextResponse.json({ flowTypes: flowNames })
  } catch (err) {
    console.error('Prefect fetch error:', err);
    return NextResponse.json({ 
      error: 'Failed to fetch flow types',
      details: err instanceof Error ? err.message : String(err)
    }, { status: 500 })
  }
}