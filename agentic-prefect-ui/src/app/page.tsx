'use client';

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";

interface FlowRun {
  id: string;
  name: string;
  state: string;
  start_time: string | null;
  expected_start_time: string | null;
  updated: string;
}

interface PrefectData {
  running: FlowRun[];
  flowType: string;
}

export default function Home() {
  const [flowData, setFlowData] = useState<PrefectData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [flowTypes, setFlowTypes] = useState<string[]>([]);
  const [selectedFlowType, setSelectedFlowType] = useState<string>('lead-flow');
  const [loadingFlowTypes, setLoadingFlowTypes] = useState(true);

  const fetchFlowTypes = async () => {
    try {
      setLoadingFlowTypes(true);
      const response = await fetch('/api/flow-types');
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Failed to fetch flow types:', response.status, errorData);
        setError(`Failed to fetch flow types: ${errorData.error || response.status}`);
        return;
      }
      
      const data = await response.json();
      console.log('Received flow types:', data);
      
      if (!data.flowTypes || !Array.isArray(data.flowTypes)) {
        console.error('Invalid flow types data:', data);
        setError('Received invalid flow types data');
        return;
      }
      
      setFlowTypes(data.flowTypes);
      setError(null);
      
      // If we have flow types and none is selected yet, select the first one
      if (data.flowTypes.length > 0) {
        if (!selectedFlowType || !data.flowTypes.includes(selectedFlowType)) {
          setSelectedFlowType(data.flowTypes[0]);
        }
      } else {
        console.log('No flow types with runs found');
      }
    } catch (err) {
      console.error('Flow types fetch error:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch flow types');
    } finally {
      setLoadingFlowTypes(false);
    }
  };

  const fetchFlowData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/flows?flowType=${encodeURIComponent(selectedFlowType)}`);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.details || `Error: ${response.status}`;
        throw new Error(errorMessage);
      }
      
      const data = await response.json();
      setFlowData(data);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch flow data';
      setError(errorMessage);
      console.error('Flow data fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch flow types on initial load
  useEffect(() => {
    fetchFlowTypes();
  }, []);

  // Fetch flow data when selected flow type changes
  useEffect(() => {
    if (selectedFlowType) {
      fetchFlowData();
      
      // Set up polling every 5 seconds
      const intervalId = setInterval(fetchFlowData, 5000);
      
      // Clean up interval on component unmount or when selected flow type changes
      return () => clearInterval(intervalId);
    }
  }, [selectedFlowType]);

  // Format date to be more readable
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  // Get status color based on state
  const getStateColor = (state: string) => {
    switch (state) {
      case 'RUNNING': return 'bg-green-500';
      case 'SCHEDULED': return 'bg-blue-500';
      case 'PENDING': return 'bg-yellow-500';
      case 'PAUSED': return 'bg-gray-500';
      case 'COMPLETED': return 'bg-green-700';
      case 'FAILED': return 'bg-red-500';
      case 'CANCELLED': return 'bg-orange-500';
      default: return 'bg-gray-300';
    }
  };

  return (
    <div className="min-h-screen p-8 font-[family-name:var(--font-geist-sans)]">
      <header className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Prefect Flow Dashboard</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Monitoring Prefect flow runs with auto-refresh every 5 seconds
        </p>
      </header>

      <main>
        {/* Flow Type Selector */}
        <div className="mb-6">
          <label htmlFor="flowTypeSelect" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Select Flow Type
          </label>
          <div className="flex gap-2">
            <select
              id="flowTypeSelect"
              value={selectedFlowType}
              onChange={(e) => setSelectedFlowType(e.target.value)}
              className="block w-full max-w-xs px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              disabled={loadingFlowTypes}
            >
              {loadingFlowTypes ? (
                <option>Loading flow types...</option>
              ) : (
                flowTypes.map((type) => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))
              )}
            </select>
            <button 
              onClick={fetchFlowTypes}
              className="p-2 rounded-md bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600"
              title="Refresh flow types"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21 2v6h-6M3 12a9 9 0 0 1 15-6.7L21 8M3 22v-6h6M21 12a9 9 0 0 1-15 6.7L3 16" />
              </svg>
            </button>
          </div>
        </div>

        {loading && !flowData ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-foreground"></div>
          </div>
        ) : error ? (
          <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
            <p className="text-red-700 dark:text-red-400">Error: {error}</p>
            <button 
              onClick={fetchFlowData}
              className="mt-2 px-4 py-2 bg-red-200 hover:bg-red-300 dark:bg-red-800 dark:hover:bg-red-700 rounded-lg transition-colors text-sm"
            >
              Retry
            </button>
          </div>
        ) : (
          <>
            <div className="mb-4 flex justify-between items-center">
              <h2 className="text-xl font-semibold">{flowData?.flowType} Runs ({flowData?.running.length || 0})</h2>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  Last updated: {new Date().toLocaleTimeString()}
                </span>
                <button 
                  onClick={fetchFlowData}
                  className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M21 2v6h-6M3 12a9 9 0 0 1 15-6.7L21 8M3 22v-6h6M21 12a9 9 0 0 1-15 6.7L3 16" />
                  </svg>
                </button>
              </div>
            </div>

            {flowData?.running && flowData.running.length > 0 ? (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {flowData.running.map((flow) => (
                  <Link href={`/${flow.id}`} key={flow.id}>
                  <div className="border dark:border-gray-800 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                    <div className="p-4 border-b dark:border-gray-800">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-medium truncate" title={flow.name}>{flow.name}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs text-white ${getStateColor(flow.state)}`}>
                          {flow.state}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate" title={flow.id}>ID: {flow.id}</p>
                    </div>
                    <div className="p-4 bg-gray-50 dark:bg-gray-900/30 text-sm">
                      <div className="grid grid-cols-[120px_1fr] gap-1">
                        <span className="text-gray-500 dark:text-gray-400">Expected Start:</span>
                        <span>{formatDate(flow.expected_start_time)}</span>
                        
                        <span className="text-gray-500 dark:text-gray-400">Actual Start:</span>
                        <span>{formatDate(flow.start_time)}</span>
                        
                        <span className="text-gray-500 dark:text-gray-400">Last Updated:</span>
                        <span>{formatDate(flow.updated)}</span>
                      </div>
                    </div>
                  </div>
                  </Link>
                ))}
              </div>    
            ) : (
              <div className="bg-gray-50 dark:bg-gray-900/30 rounded-lg p-8 text-center">
                <p className="text-gray-500 dark:text-gray-400">No {flowData?.flowType} runs found</p>
              </div>
            )}
          </>
        )}
      </main>

      <footer className="mt-12 pt-6 border-t dark:border-gray-800 text-center text-sm text-gray-500 dark:text-gray-400">
        <p>Prefect Flow Dashboard • {new Date().getFullYear()}</p>
      </footer>
    </div>
  );
}
