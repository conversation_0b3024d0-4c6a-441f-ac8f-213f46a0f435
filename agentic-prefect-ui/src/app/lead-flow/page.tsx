'use client';

import { useState, useEffect } from "react";
import Link from "next/link";

interface Deployment {
  id: string;
  name: string;
  version: string;
  is_schedule_active: boolean;
  updated: string;
  created: string;
  parameters: Record<string, any>;
}

interface DeploymentData {
  deployments: Deployment[];
}

export default function LeadFlowDeployments() {
  const [deploymentData, setDeploymentData] = useState<DeploymentData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDeploymentData = async () => {
    try {
      const response = await fetch('/api/deployments');
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      const data = await response.json();
      setDeploymentData(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch deployment data');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDeploymentData();
    
    // Set up polling every 10 seconds
    const intervalId = setInterval(fetchDeploymentData, 10000);
    
    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, []);

  // Format date to be more readable
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="min-h-screen p-8 font-[family-name:var(--font-geist-sans)]">
      <header className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Lead Flow Deployments</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Monitoring lead-flow deployments with auto-refresh every 10 seconds
        </p>
      </header>

      <main>
        <div className="mb-4">
          <Link 
            href="/"
            className="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-lg transition-colors"
          >
            Back to Dashboard
          </Link>
        </div>

        {loading && !deploymentData ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-foreground"></div>
          </div>
        ) : error ? (
          <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
            <p className="text-red-700 dark:text-red-400">{error}</p>
          </div>
        ) : (
          <>
            <div className="mb-4 flex justify-between items-center">
              <h2 className="text-xl font-semibold">Lead Flow Deployments ({deploymentData?.deployments.length || 0})</h2>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  Last updated: {new Date().toLocaleTimeString()}
                </span>
                <button 
                  onClick={fetchDeploymentData}
                  className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M21 2v6h-6M3 12a9 9 0 0 1 15-6.7L21 8M3 22v-6h6M21 12a9 9 0 0 1-15 6.7L3 16" />
                  </svg>
                </button>
              </div>
            </div>

            {deploymentData?.deployments && deploymentData.deployments.length > 0 ? (
              <div className="grid gap-4">
                {deploymentData.deployments.map((deployment) => (
                  <div key={deployment.id} className="border dark:border-gray-800 rounded-lg overflow-hidden shadow-sm">
                    <div className="p-4 border-b dark:border-gray-800">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-medium truncate" title={deployment.name}>{deployment.name}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs ${deployment.is_schedule_active ? 'bg-green-500 text-white' : 'bg-gray-500 text-white'}`}>
                          {deployment.is_schedule_active ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate" title={deployment.id}>ID: {deployment.id}</p>
                    </div>
                    <div className="p-4 bg-gray-50 dark:bg-gray-900/30 text-sm">
                      <div className="grid grid-cols-[120px_1fr] gap-1">
                        <span className="text-gray-500 dark:text-gray-400">Version:</span>
                        <span>{deployment.version}</span>
                        
                        <span className="text-gray-500 dark:text-gray-400">Created:</span>
                        <span>{formatDate(deployment.created)}</span>
                        
                        <span className="text-gray-500 dark:text-gray-400">Updated:</span>
                        <span>{formatDate(deployment.updated)}</span>
                      </div>
                    </div>
                    {deployment.parameters && Object.keys(deployment.parameters).length > 0 && (
                      <div className="p-4 border-t dark:border-gray-800">
                        <h4 className="font-medium mb-2">Parameters</h4>
                        <div className="bg-gray-100 dark:bg-gray-900 p-3 rounded overflow-x-auto">
                          <pre className="text-xs">{JSON.stringify(deployment.parameters, null, 2)}</pre>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>    
            ) : (
              <div className="bg-gray-50 dark:bg-gray-900/30 rounded-lg p-8 text-center">
                <p className="text-gray-500 dark:text-gray-400">No lead-flow deployments found</p>
              </div>
            )}
          </>
        )}
      </main>
    </div>
  );
}