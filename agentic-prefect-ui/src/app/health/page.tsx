'use client';

import { useState, useEffect } from "react";

export default function Health() {
  const [status, setStatus] = useState<string>("Loading...");
  const [lastChecked, setLastChecked] = useState<string>("");

  const checkHealth = async () => {
    try {
      const response = await fetch('/api/flows');
      if (response.ok) {
        setStatus("OK");
      } else {
        setStatus(`Error: ${response.status}`);
      }
    } catch (err) {
      setStatus("Connection Failed");
      console.error(err);
    }
    setLastChecked(new Date().toLocaleTimeString());
  };

  useEffect(() => {
    checkHealth();
    
    // Set up polling every 5 seconds
    const intervalId = setInterval(checkHealth, 5000);
    
    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, []);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-8">
      <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-8 max-w-md w-full">
        <h1 className="text-3xl font-bold mb-4 text-center">API Health Check</h1>
        
        <div className="flex justify-center my-6">
          <div className={`text-4xl font-bold px-6 py-3 rounded-lg ${
            status === "OK" 
              ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400" 
              : "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"
          }`}>
            {status}
          </div>
        </div>
        
        <div className="text-center text-sm text-gray-500 dark:text-gray-400">
          Last checked: {lastChecked || "Never"}
        </div>
        
        <div className="mt-6 text-center">
          <button 
            onClick={checkHealth}
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
          >
            Check Now
          </button>
        </div>
      </div>
    </div>
  );
}
