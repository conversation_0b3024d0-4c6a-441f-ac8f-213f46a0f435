/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],                     // you’re toggling dark mode with .dark
  content: [
    "./app/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
  ],

  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: { "2xl": "1400px" },
    },

    extend: {
      colors: {
        /* map the CSS-vars you set in globals.css */
        border:               "hsl(var(--border))",
        input:                "hsl(var(--input))",
        ring:                 "hsl(var(--ring))",
        background:           "hsl(var(--background))",
        foreground:           "hsl(var(--foreground))",

        card:                 "hsl(var(--card))",
        "card-foreground":    "hsl(var(--card-foreground))",

        primary:              "hsl(var(--primary))",
        "primary-foreground": "hsl(var(--primary-foreground))",

        secondary:              "hsl(var(--secondary))",
        "secondary-foreground": "hsl(var(--secondary-foreground))",

        muted:              "hsl(var(--muted))",
        "muted-foreground": "hsl(var(--muted-foreground))",

        accent:              "hsl(var(--accent))",
        "accent-foreground": "hsl(var(--accent-foreground))",

        destructive:              "hsl(var(--destructive))",
        "destructive-foreground": "hsl(var(--destructive-foreground))",
      },

      borderRadius: {
        lg: "var(--radius)",
      },
    },
  },

  plugins: [],   // add other plugins here if/when you need them
};
